#!/usr/bin/env python3
"""
View Context Manager for Update Data Module.

Implements Declarative Mode-Driven UI Architecture.
Manages the morphic UI behavior using centralized mode configurations.

The view changes based on context using immutable mode definitions:
- Database mode vs File utility mode
- Auto-import available vs manual selection
- All UI states defined in centralized mode configurations
"""

from ...core.services.logger import log
from .models.ui_modes import UIMode, determine_mode, get_mode_config, UIElementState, ModeConfiguration
from PySide6.QtWidgets import QComboBox, QPushButton


class UpdateDataViewManager:
    """
    Manages the dynamic state of the UpdateDataView using Declarative Mode-Driven UI Architecture.

    Uses centralized mode configurations to eliminate scattered UI state logic.
    """

    def configure_view_for_workflow(
        self, view, is_database_mode: bool, auto_import_status: dict = None
    ):
        """
        Configures the UI layout based on the selected workflow using centralized mode system.

        Args:
            view: The UpdateDataView instance to manipulate.
            is_database_mode: True if in Database Mode, False if in File-Only Mode.
            auto_import_status: Dict with auto-import status info (enabled, path, pending_files)
        """
        if auto_import_status is None:
            auto_import_status = self.get_auto_import_status()

        # Determine the appropriate UI mode using centralized logic
        auto_import_enabled = auto_import_status.get('enabled', False)
        has_pending_files = bool(auto_import_status.get('pending_files', []))

        mode = determine_mode(
            auto_import_enabled=auto_import_enabled,
            database_enabled=is_database_mode,
            has_pending_files=has_pending_files
        )

        log.debug(
            f"Configuring view for mode: {mode.value} "
            f"(db_mode={is_database_mode}, auto_import={auto_import_enabled}, "
            f"pending_files={len(auto_import_status.get('pending_files', []))})"
        )

        # Get configuration for the determined mode
        config = get_mode_config(mode)

        # Apply the mode configuration to the UI (context manager does it all)
        self._apply_mode_configuration(view, config)

        # Handle special cases for auto-import with pending files
        if mode == UIMode.DATABASE_AUTO_IMPORT and has_pending_files:
            self._display_pending_files(view, auto_import_status)

    def _display_pending_files(self, view, auto_import_status: dict):
        """
        Display pending auto-import files in the center panel.

        Args:
            view: The UpdateDataView instance
            auto_import_status: Dict with auto-import status info
        """
        try:
            pending_files = auto_import_status.get('pending_files', [])
            import_path = auto_import_status.get('path', '')

            if pending_files:
                # Create a source info dict for display
                source_info = {
                    "type": "auto_import",
                    "path": import_path,
                    "file_paths": pending_files
                }

                # Display the pending files
                view.display_selected_source(source_info)
                log.debug(f"Displayed {len(pending_files)} pending files in center panel")
            else:
                log.debug("No pending files to display")

        except Exception as e:
            log.error(f"Error displaying pending files: {e}")
            # Fallback to welcome pane
            try:
                view.center_display.show_welcome_pane()
            except Exception as fallback_error:
                log.error(f"Fallback to welcome pane also failed: {fallback_error}")

    def configure_view_for_mode(self, view, mode: UIMode, auto_import_status: dict = None):
        """
        Configure view for a specific UI mode (alternative interface).

        Args:
            view: The UpdateDataView instance
            mode: The UI mode to configure for
            auto_import_status: Optional auto-import status info
        """
        if auto_import_status is None:
            auto_import_status = self.get_auto_import_status()

        log.debug(f"Configuring view for specific mode: {mode.value}")

        # Get and apply configuration
        config = get_mode_config(mode)
        self._apply_mode_configuration(view, config)

        # Handle special cases
        if mode == UIMode.DATABASE_AUTO_IMPORT and auto_import_status.get('pending_files'):
            self._display_pending_files(view, auto_import_status)

    def get_auto_import_status(self) -> dict:
        """Get current auto-import status including pending files.

        Returns:
            Dict with keys: enabled, path, pending_files, last_check
        """
        from ...core.config import config
        from ...core.config.keys import ConfigKeys

        enabled = config.get_value(ConfigKeys.AutoImport.ENABLED, False)
        import_path = config.get_value(ConfigKeys.AutoImport.IMPORT_PATH, "")

        status = {
            'enabled': enabled,
            'path': import_path,
            'pending_files': [],
            'last_check': None
        }

        # If enabled and path exists, check for pending files
        if enabled and import_path:
            status['pending_files'] = self._scan_for_pending_files(import_path)

        return status

    def _scan_for_pending_files(self, import_path: str) -> list:
        """Scan import folder for new CSV files.

        Args:
            import_path: Path to the auto-import folder

        Returns:
            List of file paths that need to be imported
        """
        try:
            from pathlib import Path
            import_dir = Path(import_path)

            if not import_dir.exists():
                log.warning(f"Auto-import folder does not exist: {import_path}")
                return []

            # Find all CSV files in the import directory
            csv_files = list(import_dir.glob("*.csv"))

            # TODO: Filter out already processed files
            # For now, return all CSV files
            pending_files = [str(f) for f in csv_files]

            log.debug(f"Found {len(pending_files)} pending files in {import_path}")
            return pending_files

        except Exception as e:
            log.error(f"Error scanning auto-import folder: {e}")
            return []

    def get_current_mode(self, is_database_mode: bool, auto_import_status: dict = None) -> UIMode:
        """
        Get the current UI mode based on settings.

        Args:
            is_database_mode: Whether database mode is enabled
            auto_import_status: Optional auto-import status info

        Returns:
            The current UI mode
        """
        if auto_import_status is None:
            auto_import_status = self.get_auto_import_status()

        return determine_mode(
            auto_import_enabled=auto_import_status.get('enabled', False),
            database_enabled=is_database_mode,
            has_pending_files=bool(auto_import_status.get('pending_files', []))
        )

    def apply_mode_transition(self, view, from_mode: UIMode, to_mode: UIMode, preserve_state: bool = True):
        """
        Apply a mode transition with optional state preservation.

        Args:
            view: The UpdateDataView instance
            from_mode: The current mode
            to_mode: The target mode
            preserve_state: Whether to preserve user selections where possible
        """
        log.debug(f"Transitioning from {from_mode.value} to {to_mode.value}")

        # Get target configuration
        config = get_mode_config(to_mode)

        # Apply configuration (context manager handles state preservation)
        self._apply_mode_configuration(view, config)

        log.debug(f"Mode transition completed: {from_mode.value} -> {to_mode.value}")

    # ====== UI Application Methods (Context Manager Does It All) ======

    def _apply_mode_configuration(self, view, config: ModeConfiguration) -> None:
        """
        Apply complete mode configuration to view.

        Args:
            view: The update data view instance
            config: The mode configuration to apply
        """
        try:
            log.debug(f"Applying UI configuration for center panel type: {config.center_panel_type}")

            # Apply to left panel elements
            left_buttons = view.left_buttons

            # Source section
            self._apply_to_combo(left_buttons.source_combo, config.source_combo)
            self._apply_to_button(left_buttons.source_select_btn, config.source_button)

            # Save section
            self._apply_to_combo(left_buttons.save_combo, config.save_combo)
            self._apply_to_button(left_buttons.save_select_btn, config.save_button)

            # Database section
            self._apply_to_checkbox(left_buttons.db_update_checkbox, config.database_checkbox)

            # Process section
            self._apply_to_button(left_buttons.process_btn, config.process_button)

            # Apply center panel configuration
            self._apply_center_panel_config(view, config.center_panel_type)

            log.debug("UI configuration applied successfully")

        except Exception as e:
            log.error(f"Error applying UI configuration: {e}")
            raise

    def _apply_to_combo(self, combo: QComboBox, state: UIElementState, preserve_selection: bool = True) -> None:
        """Apply state to combo box."""
        if state.options:
            # Store current selection if preserving
            current_text = combo.currentText() if preserve_selection else None

            # Update options
            combo.clear()
            combo.addItems(state.options)

            # Set selection priority: state.text > preserved selection > first option
            if state.text and state.text in state.options:
                combo.setCurrentText(state.text)
            elif current_text and current_text in state.options:
                combo.setCurrentText(current_text)
            elif state.options:
                combo.setCurrentIndex(0)

        # Apply visibility and enabled state
        combo.setVisible(state.visible)
        combo.setEnabled(state.enabled)

        # Apply tooltip
        if state.tooltip:
            combo.setToolTip(state.tooltip)

    def _apply_to_button(self, button: QPushButton, state: UIElementState) -> None:
        """Apply state to button."""
        if state.text:
            button.setText(state.text)

        button.setVisible(state.visible)
        button.setEnabled(state.enabled)

        if state.tooltip:
            button.setToolTip(state.tooltip)

    def _apply_to_checkbox(self, checkbox, state: UIElementState) -> None:
        """Apply state to checkbox (handles both QCheckBox and LabeledCheckBox)."""
        if state.checked is not None:
            if hasattr(checkbox, 'set_checked'):
                # LabeledCheckBox from shared components
                checkbox.set_checked(state.checked)
            else:
                # Standard QCheckBox
                checkbox.setChecked(state.checked)

        checkbox.setVisible(state.visible)
        checkbox.setEnabled(state.enabled)

        if state.tooltip:
            checkbox.setToolTip(state.tooltip)

    def _apply_center_panel_config(self, view, panel_type: str) -> None:
        """Apply center panel configuration."""
        try:
            if panel_type == "welcome":
                view.center_display.show_welcome_pane()
            elif panel_type == "file_pane":
                view.center_display.show_file_pane()
            elif panel_type == "processing":
                # Future: show processing pane
                view.center_display.show_welcome_pane()
            else:
                log.warning(f"Unknown center panel type: {panel_type}, defaulting to welcome")
                view.center_display.show_welcome_pane()

        except Exception as e:
            log.error(f"Error applying center panel config: {e}")
            # Fallback to welcome pane
            try:
                view.center_display.show_welcome_pane()
            except Exception as fallback_error:
                log.error(f"Fallback to welcome pane also failed: {fallback_error}")

    def should_open_update_data_on_startup(self) -> bool:
        """Check if app should open to Update Data module on startup.

        This checks if there are pending files in the auto-import folder.

        Returns:
            True if Update Data should be the initial module
        """
        auto_import_status = self.get_auto_import_status()

        # Open to Update Data if auto-import is enabled and has pending files
        if (auto_import_status.get('enabled', False) and
            auto_import_status.get('pending_files')):
            log.info(f"Opening to Update Data - found {len(auto_import_status['pending_files'])} pending files")
            return True

        return False
