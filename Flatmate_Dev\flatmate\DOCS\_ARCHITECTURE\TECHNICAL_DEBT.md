# Technical Debt - Flatmate Application

**Last Updated**: 2025-01-21
**Status**: ACTIVE_TRACKING

## 🔥 Critical Technical Debt (Immediate Action Required)

### 1. File Browser Infinite Recursion
**Location**: `src/fm/modules/update_data/_view/center_panel/widgets/file_browser.py:257`
**Severity**: CRITICAL (Application Crash)
**Impact**: Users cannot select auto-import folder without crashing app
**Root Cause**: Recursive path resolution without proper termination conditions
**Estimated Fix Time**: 30 minutes
**Dependencies**: None
**Risk**: HIGH - Blocks core functionality

**Technical Details**:
```python
# Current problematic code:
def _ensure_folder_path(self, folder_path, source_dir):
    parent_dir = os.path.dirname(folder_path)
    parent_item = self._ensure_folder_path(parent_dir, source_dir)  # Infinite recursion
```

**Proposed Solution**: Add path normalization and termination conditions

### 2. Morphic UI State Management
**Location**: `src/fm/modules/update_data/_view/left_panel/widgets/widgets.py`
**Severity**: HIGH (UI Unusability)
**Impact**: UI becomes unusable when toggling database mode
**Root Cause**: Incomplete state preservation during morphic transitions
**Estimated Fix Time**: 45 minutes
**Dependencies**: None
**Risk**: HIGH - Core feature broken

**Technical Details**:
- Combo box options disappear when database checkbox is deselected
- Options don't reappear when checkbox is re-enabled
- No state preservation mechanism between UI modes

**Proposed Solution**: Implement state preservation and restoration logic

## 🚨 High Priority Technical Debt

### 3. Centralized Mode State Architecture (ARCHITECTURAL)
**Location**: `src/fm/modules/update_data/` - scattered mode logic
**Severity**: HIGH (Architecture Improvement)
**Impact**: Eliminates root cause of all UI state management bugs
**Root Cause**: Mode states scattered across multiple files without central definition
**Estimated Fix Time**: 2 hours
**Dependencies**: None (can be done independently)
**Risk**: LOW - Well-defined architectural improvement

**User Insight**: "enumerate modes, and define in a pydantic dataclass in one place, what state each element should be in in a given mode.. use this to drive the context manager"

**Technical Details**:
- Current: Mode logic scattered across widgets.py, view_context_manager.py, presenter.py
- Proposed: Single Pydantic model defining all UI states for each mode
- Benefits: Type safety, immutable configs, single source of truth, eliminates state bugs

**Proposed Implementation**:
```python
# New: src/fm/modules/update_data/models/ui_modes.py
class UIMode(str, Enum):
    DATABASE_AUTO_IMPORT = "database_auto_import"
    DATABASE_MANUAL = "database_manual"
    FILE_UTILITY = "file_utility"

class ModeConfiguration(BaseModel):
    source_combo: UIElementState
    source_button: UIElementState
    save_combo: UIElementState
    database_checkbox: UIElementState
    process_button: UIElementState
```

**Impact**: This single change would prevent ALL current UI state management bugs

### 4. Center Panel Display Persistence
**Location**: `src/fm/modules/update_data/view_context_manager.py`
**Severity**: HIGH (Data Loss)
**Impact**: File tree disappears when changing database settings
**Root Cause**: Missing refresh logic in view context manager
**Estimated Fix Time**: 30 minutes
**Dependencies**: Fix #2 (UI State Management)
**Risk**: MEDIUM - User confusion, workflow interruption

### 4. Save Location UI Integration
**Location**: Multiple files - presenter/view integration
**Severity**: MEDIUM (User Experience)
**Impact**: Save location not reflected in UI after selection
**Root Cause**: Incomplete integration between presenter and view components
**Estimated Fix Time**: 30 minutes
**Dependencies**: None
**Risk**: LOW - Functional but confusing

## 📋 Medium Priority Technical Debt

### 5. Old Info Bar System
**Location**: Various modules using old info bar
**Severity**: MEDIUM (Code Consistency)
**Impact**: Inconsistent messaging system across application
**Root Cause**: Legacy info bar not fully replaced with new messaging service
**Estimated Fix Time**: 2 hours
**Dependencies**: None
**Risk**: LOW - Cosmetic/consistency issue

**User Request**: "piss the old info bar off"

### 6. Master File Logic Missing
**Location**: `src/fm/modules/update_data/ud_presenter.py`
**Severity**: MEDIUM (Feature Incomplete)
**Impact**: Missing create/update master functionality based on file tracker
**Root Cause**: Feature not implemented in current refactor
**Estimated Fix Time**: 1 hour
**Dependencies**: Master file tracker service
**Risk**: LOW - Enhancement rather than bug

## 🔧 Low Priority Technical Debt

### 7. Error Handling Improvements
**Location**: File operation methods across update_data module
**Severity**: LOW (Robustness)
**Impact**: Poor error messages for edge cases
**Root Cause**: Minimal error handling in file operations
**Estimated Fix Time**: 1 hour
**Dependencies**: None
**Risk**: LOW - Edge case handling

### 8. Path Validation Enhancements
**Location**: File selection and validation logic
**Severity**: LOW (Robustness)
**Impact**: Potential issues with unusual file paths
**Root Cause**: Basic path validation without edge case handling
**Estimated Fix Time**: 45 minutes
**Dependencies**: None
**Risk**: LOW - Edge case handling

## 📊 Technical Debt Metrics

### By Severity
- **CRITICAL**: 1 item (30 min)
- **HIGH**: 4 items (225 min)
- **MEDIUM**: 2 items (180 min)
- **LOW**: 2 items (105 min)

**Total Estimated Fix Time**: 9 hours

### By Risk Level
- **HIGH RISK**: 3 items (blocks core functionality)
- **MEDIUM RISK**: 1 item (workflow interruption)
- **LOW RISK**: 4 items (enhancements/polish)

### By Dependencies
- **No Dependencies**: 6 items (can be fixed independently)
- **Has Dependencies**: 2 items (require other fixes first)

## 🎯 Recommended Fix Order

### Phase 1: Critical Fixes (1.75 hours)
1. **File Browser Infinite Recursion** (30 min) - CRITICAL
2. **Morphic UI State Management** (45 min) - HIGH
3. **Center Panel Display Persistence** (30 min) - HIGH
4. **Save Location UI Integration** (30 min) - MEDIUM

### Phase 1.5: Architectural Improvement (2 hours) - RECOMMENDED
**Centralized Mode State Architecture** (2 hours) - HIGH
- **Why Now**: Eliminates root cause of UI state bugs
- **Impact**: Prevents future state management issues
- **Alternative**: Can be done after Phase 1 as refactor

### Phase 2: Feature Completion (3 hours)
5. **Old Info Bar Removal** (2 hours) - MEDIUM
6. **Master File Logic** (1 hour) - MEDIUM

### Phase 3: Polish & Robustness (2.25 hours)
7. **Error Handling Improvements** (1 hour) - LOW
8. **Path Validation Enhancements** (45 min) - LOW

## 🔄 Debt Prevention Strategies

### Code Review Checklist
- [ ] Path operations have termination conditions
- [ ] UI state changes preserve user context
- [ ] Error handling covers edge cases
- [ ] Integration between components is complete

### Architecture Guidelines
- [ ] Use base widgets from shared components
- [ ] Implement proper state management patterns
- [ ] Follow consistent error handling patterns
- [ ] Complete feature integration before marking done

### Testing Requirements
- [ ] Test edge cases that could cause infinite loops
- [ ] Test UI state transitions thoroughly
- [ ] Test error conditions and recovery
- [ ] Test complete user workflows end-to-end

---

**Next Review**: After critical fixes are implemented
**Responsibility**: Development team
**Tracking**: Update this document after each fix session
