[] update data base label + chb  ivestigate why does it have a black back ground, why isnt ti like the
-fix use gui shared widgets

# database mode switching
[] mode switching, however the differen modes are currently configures, ticking and unticking update database should change them as neccesary
- modifier = update_database_changed
selected = database mode
unselected = file utility mode
currently does not switch back 



Actually utimatetely this might be incorrect
the important thing is to be able to modify the gui depending on various conditions.

Actual logic might be:

logic: select source 
what ever the source is 
database can be updated or not
